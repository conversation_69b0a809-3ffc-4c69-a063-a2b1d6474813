<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>账号配置生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 25px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 3px;
        }
        .section h3 {
            margin-top: 0;
            color: #555;
            border-bottom: 2px solid #eee;
            padding-bottom: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 150px;
            font-weight: bold;
            color: #666;
        }
        input[type="text"], input[type="password"], select, textarea {
            width: 300px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
            font-size: 14px;
        }
        input[type="checkbox"] {
            margin-right: 5px;
        }
        textarea {
            height: 60px;
            resize: vertical;
        }
        .skill-item {
            background-color: #f9f9f9;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
        }
        .skill-item input {
            width: 80px;
            margin-right: 10px;
        }
        .buttons {
            text-align: center;
            margin-top: 30px;
        }
        button {
            padding: 10px 20px;
            margin: 0 10px;
            font-size: 16px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-generate {
            background-color: #4CAF50;
            color: white;
        }
        .btn-generate:hover {
            background-color: #45a049;
        }
        .btn-clear {
            background-color: #f44336;
            color: white;
        }
        .btn-clear:hover {
            background-color: #da190b;
        }
        #output {
            margin-top: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .array-input {
            width: 400px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>账号配置生成器</h1>
        
        <!-- 基础配置 -->
        <div class="section">
            <h3>基础配置</h3>
            <div class="form-group">
                <label>服务器:</label>
                <input type="text" id="server" value="x12">
            </div>
            <div class="form-group">
                <label>账号:</label>
                <input type="text" id="account" value="memorysiliao">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="password" value="000000">
            </div>
            <div class="form-group">
                <label>角色序号:</label>
                <input type="text" id="roleIndex" value="0">
            </div>
        </div>

        <!-- 代理配置 -->
        <div class="section">
            <h3>代理配置</h3>
            <div class="form-group">
                <label>代理账号:</label>
                <input type="text" id="proxyAccount" value="FQTUENGM">
            </div>
            <div class="form-group">
                <label>代理密码:</label>
                <input type="text" id="proxyPassword" value="35B7E37DAF97">
            </div>
        </div>

        <!-- 任务开关 -->
        <div class="section">
            <h3>任务开关</h3>
            <div class="form-group">
                <label><input type="checkbox" id="dailyTuoba" checked>日常拖把开关</label>
            </div>
            <div class="form-group">
                <label><input type="checkbox" id="dailyMohua">日常魔化开关</label>
            </div>
            <div class="form-group">
                <label><input type="checkbox" id="activityRuyi">活动如意开关</label>
            </div>
            <div class="form-group">
                <label><input type="checkbox" id="activityTongtian">活动通天开关</label>
            </div>
            <div class="form-group">
                <label><input type="checkbox" id="activityNiwushuang">活动逆无双开关</label>
            </div>
            <div class="form-group">
                <label><input type="checkbox" id="activityLuohan">活动罗汉开关</label>
            </div>
        </div>

        <!-- 闲时任务 -->
        <div class="section">
            <h3>闲时任务</h3>
            <div class="form-group">
                <label>闲时任务:</label>
                <select id="idleTask">
                    <option value="闲时_禅壹">闲时_禅壹</option>
                    <option value="闲时_禅伍">闲时_禅伍</option>
                    <option value="闲时_道壹">闲时_道壹</option>
                    <option value="闲时_打工">闲时_打工</option>
                    <option value="闲时_刷怪" selected>闲时_刷怪</option>
                </select>
            </div>
            <div class="form-group">
                <label>换装包前缀:</label>
                <input type="text" id="equipPrefix" value="刷怪换装包">
            </div>
            <div class="form-group">
                <label>卡级配置:</label>
                <input type="text" id="levelLimit" value="90" placeholder="输入0-90的数字，或留空表示不卡级">
            </div>
        </div>

        <!-- 通天配置 -->
        <div class="section">
            <h3>通天配置</h3>
            <div class="form-group">
                <label>刷怪武器:</label>
                <input type="text" id="tongtianWeapon" value="强击长矛">
            </div>
            <div class="form-group">
                <label>职业技能:</label>
                <input type="text" id="tongtianSkill" value="霸王枪">
            </div>
            <div class="form-group">
                <label>释放技能列表:</label>
                <textarea id="tongtianSkillList" class="array-input">猛抽,致命一击,强力打击[1级]</textarea>
            </div>
        </div>

        <!-- 刷怪配置 -->
        <div class="section">
            <h3>刷怪配置</h3>
            <div class="form-group">
                <label>刷怪武器:</label>
                <input type="text" id="brushWeapon" value="刺客之刃">
            </div>
            <div class="form-group">
                <label>职业技能:</label>
                <input type="text" id="brushSkill" value="龙葵匕刃">
            </div>
            <div class="form-group">
                <label>释放技能列表:</label>
                <textarea id="brushSkillList" class="array-input">强力打击[1级],凿击[1级],猛抽,致命一击</textarea>
            </div>
            <div class="form-group">
                <label>刷怪起点:</label>
                <input type="text" id="brushStart" value="map.kasitepingyuan|卡斯特平原34" style="width:400px;">
            </div>
            <div class="form-group">
                <label>刷怪路线:</label>
                <textarea id="brushRoute" class="array-input">卡斯特平原34,卡斯特平原12,卡斯特平原15,卡斯特平原41,卡斯特平原19,卡斯特平原42,卡斯特平原21,卡斯特平原17,卡斯特平原18,卡斯特平原16,卡斯特平原36,卡斯特平原35</textarea>
            </div>
            <div class="form-group">
                <label>刷怪目标:</label>
                <textarea id="brushTargets" class="array-input">豆芽,蚱蜢,螃蟹</textarea>
            </div>
            <div class="form-group">
                <label>伏击技能:</label>
                <input type="text" id="ambushSkill" value="伏击[一级]">
            </div>
            <div class="form-group">
                <label>售卖圈数:</label>
                <input type="text" id="sellCycles" value="5">
            </div>
            <div class="form-group">
                <label>售卖道具列表:</label>
                <textarea id="sellItems" class="array-input">猎鹰之,巨猿之,野猪之,猛虎之,达拉,宝箱,民兵,针织,螃蟹,亚麻,普通,农夫,园艺,能量之,敏捷之,鼓舞,坚韧,精神,奥术,猛击,炸弹,战斗怒吼,暴怒,残忍,雄鹰之,灵猴之,巨鲸之</textarea>
            </div>
        </div>

        <!-- 技能配置 -->
        <div class="section">
            <h3>技能配置</h3>
            <div id="skillsContainer">
                <div class="skill-item">
                    <label>出血[6级]:</label>
                    前摇<input type="text" value="0" data-skill="出血[6级]" data-prop="前摇">
                    后摇<input type="text" value="0" data-skill="出血[6级]" data-prop="后摇">
                    CD<input type="text" value="5" data-skill="出血[6级]" data-prop="CD">
                </div>
                <div class="skill-item">
                    <label>冥火爪[11级]:</label>
                    前摇<input type="text" value="0" data-skill="冥火爪[11级]" data-prop="前摇">
                    后摇<input type="text" value="0" data-skill="冥火爪[11级]" data-prop="后摇">
                    CD<input type="text" value="10" data-skill="冥火爪[11级]" data-prop="CD">
                </div>
                <div class="skill-item">
                    <label>凿击[1级]:</label>
                    前摇<input type="text" value="0" data-skill="凿击[1级]" data-prop="前摇">
                    后摇<input type="text" value="0" data-skill="凿击[1级]" data-prop="后摇">
                    CD<input type="text" value="20" data-skill="凿击[1级]" data-prop="CD">
                </div>
                <div class="skill-item">
                    <label>强力打击[1级]:</label>
                    前摇<input type="text" value="3" data-skill="强力打击[1级]" data-prop="前摇">
                    后摇<input type="text" value="0" data-skill="强力打击[1级]" data-prop="后摇">
                    CD<input type="text" value="6" data-skill="强力打击[1级]" data-prop="CD">
                </div>
                <div class="skill-item">
                    <label>猛抽:</label>
                    前摇<input type="text" value="0" data-skill="猛抽" data-prop="前摇">
                    后摇<input type="text" value="0" data-skill="猛抽" data-prop="后摇">
                    CD<input type="text" value="30" data-skill="猛抽" data-prop="CD">
                </div>
                <div class="skill-item">
                    <label>致命一击:</label>
                    前摇<input type="text" value="3" data-skill="致命一击" data-prop="前摇">
                    后摇<input type="text" value="0" data-skill="致命一击" data-prop="后摇">
                    CD<input type="text" value="40" data-skill="致命一击" data-prop="CD">
                </div>
            </div>
        </div>

        <!-- 按钮 -->
        <div class="buttons">
            <button class="btn-generate" onclick="generateConfig()">生成配置</button>
            <button class="btn-clear" onclick="clearOutput()">清空输出</button>
        </div>

        <!-- 输出区域 -->
        <div id="output"></div>
    </div>

    <script>
        function generateConfig() {
            var config = {
                "服务器": document.getElementById('server').value,
                "账号": document.getElementById('account').value,
                "密码": document.getElementById('password').value,
                "角色序号": document.getElementById('roleIndex').value,
                "代理配置": {
                    "代理账号": document.getElementById('proxyAccount').value,
                    "代理密码": document.getElementById('proxyPassword').value
                },
                "日常拖把开关": document.getElementById('dailyTuoba').checked,
                "日常魔化开关": document.getElementById('dailyMohua').checked,
                "活动如意开关": document.getElementById('activityRuyi').checked,
                "活动通天开关": document.getElementById('activityTongtian').checked,
                "活动逆无双开关": document.getElementById('activityNiwushuang').checked,
                "活动罗汉开关": document.getElementById('activityLuohan').checked,
                "闲时任务": document.getElementById('idleTask').value,
                "换装包前缀": document.getElementById('equipPrefix').value,
                "卡级配置": (function() {
                    var levelValue = document.getElementById('levelLimit').value.trim();
                    if (levelValue === '' || levelValue === 'null' || levelValue === 'None') {
                        return null;
                    }
                    var num = parseInt(levelValue);
                    return (num >= 0 && num <= 90) ? num : null;
                })(),
                "通天配置": {
                    "刷怪武器": document.getElementById('tongtianWeapon').value,
                    "职业技能": document.getElementById('tongtianSkill').value,
                    "释放技能列表": document.getElementById('tongtianSkillList').value.split(',').map(function(s) { return s.trim(); })
                },
                "刷怪配置": {
                    "刷怪武器": document.getElementById('brushWeapon').value,
                    "职业技能": document.getElementById('brushSkill').value,
                    "释放技能列表": document.getElementById('brushSkillList').value.split(',').map(function(s) { return s.trim(); }),
                    "刷怪起点": document.getElementById('brushStart').value,
                    "刷怪路线": document.getElementById('brushRoute').value,
                    "刷怪目标": document.getElementById('brushTargets').value.split(',').map(function(s) { return s.trim(); }),
                    "伏击技能": document.getElementById('ambushSkill').value,
                    "每圈结束执行的其他代码": null,
                    "跑图多少圈进行一次售卖": parseInt(document.getElementById('sellCycles').value),
                    "售卖道具列表": document.getElementById('sellItems').value.split(',').map(function(s) { return s.trim(); })
                },
                "技能配置": {}
            };

            // 处理技能配置
            var skillInputs = document.querySelectorAll('#skillsContainer input');
            for (var i = 0; i < skillInputs.length; i++) {
                var input = skillInputs[i];
                var skillName = input.getAttribute('data-skill');
                var prop = input.getAttribute('data-prop');

                if (!config["技能配置"][skillName]) {
                    config["技能配置"][skillName] = {"剩余CD": 0};
                }
                config["技能配置"][skillName][prop] = parseInt(input.value) || 0;
            }

            var jsonString = JSON.stringify(config, null, 4);
            document.getElementById('output').textContent = jsonString;
        }

        function clearOutput() {
            document.getElementById('output').textContent = '';
        }

        // 页面加载完成后自动生成一次配置
        window.onload = function() {
            generateConfig();
        };
    </script>
</body>
</html>
